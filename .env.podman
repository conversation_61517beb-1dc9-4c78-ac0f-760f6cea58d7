# Chatwoot Podman开发环境配置文件

# =====================================================
# 数据库配置
# =====================================================
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DATABASE=chatwoot_dev
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=chatwoot123

# =====================================================
# Redis配置
# =====================================================
REDIS_URL=redis://:chatwoot123@redis:6379/0
REDIS_PASSWORD=chatwoot123

# =====================================================
# Rails应用配置
# =====================================================
RAILS_ENV=development
NODE_ENV=development
SECRET_KEY_BASE=development_secret_key_base_at_least_30_chars_long_for_security
RAILS_LOG_TO_STDOUT=true
RAILS_SERVE_STATIC_FILES=true
RAILS_MAX_THREADS=5

# =====================================================
# 邮件服务配置
# =====================================================
SMTP_ADDRESS=mailhog
SMTP_PORT=1025
SMTP_DOMAIN=chatwoot.dev
MAILER_SMTP_DOMAIN=chatwoot.dev
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_AUTHENTICATION=
SMTP_ENABLE_STARTTLS_AUTO=false

# =====================================================
# 前端开发服务器配置
# =====================================================
WEBPACKER_DEV_SERVER_HOST=vite
WEBPACKER_DEV_SERVER_PORT=3036
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=3036
VITE_HMR_PORT=3036

# 文件监听优化（适用于容器环境）
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# =====================================================
# Sidekiq配置
# =====================================================
SIDEKIQ_CONCURRENCY=5
SIDEKIQ_TIMEOUT=25

# =====================================================
# 应用配置
# =====================================================
# 应用域名
FRONTEND_URL=http://localhost:3036
BACKEND_URL=http://localhost:3000

# 文件存储
ACTIVE_STORAGE_SERVICE=local
STORAGE_BUCKET_NAME=chatwoot-dev

# 日志级别
LOG_LEVEL=debug

# =====================================================
# 开发工具配置
# =====================================================
# 是否启用开发工具
ENABLE_DEVELOPER_TOOLS=true

# 是否显示详细错误信息
SHOW_DETAILED_ERRORS=true

# 是否启用代码重载
ENABLE_CODE_RELOADING=true

# =====================================================
# 安全配置（开发环境）
# =====================================================
# 跨域配置
CORS_ORIGINS=http://localhost:3036,http://localhost:3000

# 会话配置
SESSION_TIMEOUT=86400

# =====================================================
# 特性开关
# =====================================================
# 启用的特性
ENABLE_FEATURE_EMAIL_TEMPLATES=true
ENABLE_FEATURE_WEBHOOKS=true
ENABLE_FEATURE_INTEGRATIONS=true
ENABLE_FEATURE_CAMPAIGNS=true
ENABLE_FEATURE_REPORTS=true

# =====================================================
# 第三方服务配置（开发环境可选）
# =====================================================
# Sentry（错误追踪）
# SENTRY_DSN=

# Google Analytics
# GOOGLE_ANALYTICS_ID=

# Pusher（实时通信）
# PUSHER_APP_ID=
# PUSHER_KEY=
# PUSHER_SECRET=
# PUSHER_CLUSTER=

# AWS S3（文件存储）
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=
# AWS_S3_BUCKET_NAME=

# =====================================================
# 调试配置
# =====================================================
# Rails调试
RAILS_LOG_LEVEL=debug
RAILS_BACKTRACE_CLEANER_ENABLED=false

# SQL查询日志
LOG_SQL_QUERIES=true

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true
