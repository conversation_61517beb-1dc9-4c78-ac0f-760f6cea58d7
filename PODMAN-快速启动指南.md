# 🚀 Chatwoot Podman开发环境快速启动指南

## 📋 概述

本指南将帮助您在5分钟内使用Podman启动Chatwoot开发环境，支持代码热重载和完整的开发工具链。

## 🔧 1. 环境准备

### 安装Podman

**macOS:**
```bash
# 安装Podman
brew install podman

# 初始化Podman Machine
podman machine init --cpus 4 --memory 8192 --disk-size 50
podman machine start
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update && sudo apt install podman -y
```

**验证安装:**
```bash
podman --version
podman info | grep -i rootless
```

### 可选：安装podman-compose
```bash
# 使用pip安装（推荐）
pip3 install podman-compose

# 或使用系统包管理器
# Ubuntu: sudo apt install podman-compose
# macOS: brew install podman-compose
```

## 🚀 2. 一键启动

### 方式一：使用管理脚本（推荐）

```bash
# 1. 进入项目目录
cd /Users/<USER>/Documents/cursor/chatwoot-v2

# 2. 赋予执行权限
chmod +x scripts/podman-dev.sh

# 3. 启动开发环境
./scripts/podman-dev.sh start
```

### 方式二：使用podman-compose

```bash
# 1. 复制环境配置
cp .env.podman .env

# 2. 构建镜像
podman build -t chatwoot:development --target development -f Containerfile.dev .

# 3. 启动服务
podman-compose -f docker-compose.podman.yaml up -d

# 4. 初始化数据库
podman exec chatwoot_rails bundle exec rails db:create db:migrate db:seed
```

### 方式三：使用现有Pod配置

```bash
# 1. 创建卷
./scripts/create-volumes.sh

# 2. 构建镜像
podman build -t chatwoot:development --target development -f Containerfile.dev .

# 3. 启动Pod
podman play kube chatwoot-full-macos.yaml

# 4. 初始化数据库
sleep 30
podman exec chatwoot-full-pod-rails bundle exec rails db:create db:migrate db:seed
```

## 🎯 3. 访问服务

启动成功后，通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| 🌐 **Chatwoot Web应用** | http://localhost:3036 | 主要开发界面 |
| 🔧 **Rails API** | http://localhost:3000 | 后端API服务 |
| 📧 **MailHog** | http://localhost:8025 | 邮件测试工具 |
| 💾 **PostgreSQL** | localhost:5432 | 数据库连接 |
| 🔄 **Redis** | localhost:6379 | 缓存服务 |

### 默认登录信息
- **数据库**: `chatwoot_dev` / `postgres` / `chatwoot123`
- **Redis**: 密码 `chatwoot123`

## 📊 4. 常用管理命令

### 使用管理脚本

```bash
# 查看服务状态
./scripts/podman-dev.sh status

# 查看日志
./scripts/podman-dev.sh logs rails
./scripts/podman-dev.sh logs vite
./scripts/podman-dev.sh logs sidekiq

# 进入容器
./scripts/podman-dev.sh exec rails
./scripts/podman-dev.sh exec rails "bundle exec rails console"

# 重启服务
./scripts/podman-dev.sh restart

# 停止服务
./scripts/podman-dev.sh stop

# 重置环境（删除所有数据）
./scripts/podman-dev.sh reset
```

### 直接使用Podman命令

```bash
# 查看容器状态
podman ps

# 查看日志
podman logs -f chatwoot_rails
podman logs -f chatwoot_vite

# 进入容器
podman exec -it chatwoot_rails /bin/sh
podman exec -it chatwoot_rails bundle exec rails console

# 重启容器
podman restart chatwoot_rails
podman restart chatwoot_vite
```

## 🛠️ 5. 开发工作流

### 代码修改
- **前端代码**: 支持热重载，修改后自动刷新浏览器
- **后端代码**: 修改Ruby文件后需要重启Rails服务
- **配置文件**: 修改后需要重启相应服务

### 数据库操作
```bash
# 运行迁移
podman exec chatwoot_rails bundle exec rails db:migrate

# 重置数据库
podman exec chatwoot_rails bundle exec rails db:drop db:create db:migrate db:seed

# 查看数据库状态
podman exec chatwoot_rails bundle exec rails db:version
```

### 安装新依赖
```bash
# 安装Ruby Gem
podman exec chatwoot_rails bundle install

# 安装Node.js包
podman exec chatwoot_vite pnpm install
```

## 🔍 6. 故障排除

### 常见问题

**问题1: 端口冲突**
```bash
# 检查端口占用
lsof -i :3000
lsof -i :3036
lsof -i :5432

# 停止占用端口的进程
sudo kill -9 <PID>
```

**问题2: 容器启动失败**
```bash
# 查看容器日志
podman logs chatwoot_rails
podman logs chatwoot_postgres

# 检查镜像是否存在
podman images | grep chatwoot
```

**问题3: 数据库连接失败**
```bash
# 检查PostgreSQL状态
podman exec chatwoot_postgres pg_isready -U postgres

# 重启数据库
podman restart chatwoot_postgres
```

**问题4: 前端热重载不工作**
```bash
# 检查Vite服务状态
podman logs chatwoot_vite

# 重启Vite服务
podman restart chatwoot_vite
```

### 完全重置环境
```bash
# 使用管理脚本重置
./scripts/podman-dev.sh reset

# 或手动重置
podman-compose -f docker-compose.podman.yaml down -v
podman volume prune -f
podman image rm chatwoot:development
```

## 📈 7. 性能优化

### 资源配置
```bash
# 调整Podman Machine资源（macOS）
podman machine stop
podman machine rm
podman machine init --cpus 6 --memory 12288 --disk-size 100
podman machine start
```

### 开发环境优化
- 使用SSD存储提升I/O性能
- 增加内存分配给Podman Machine
- 启用文件监听轮询（已在配置中启用）

## 🎉 8. 开始开发

环境启动成功后：

1. 访问 http://localhost:3036 查看Chatwoot界面
2. 访问 http://localhost:8025 查看MailHog邮件测试工具
3. 修改代码文件，观察热重载效果
4. 使用 `podman exec -it chatwoot_rails bundle exec rails console` 进入Rails控制台

## 📚 9. 更多资源

- [完整部署指南](./CHATWOOT-PODMAN-部署指南.md)
- [Podman官方文档](https://podman.io/docs)
- [Chatwoot开发文档](https://www.chatwoot.com/docs/contributing-guide)

---

**提示**: 如果遇到问题，请查看日志文件或使用 `./scripts/podman-dev.sh help` 获取更多帮助。
