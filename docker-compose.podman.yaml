version: '3.8'

services:
  # PostgreSQL数据库服务
  postgres:
    image: docker.io/pgvector/pgvector:pg16
    container_name: chatwoot_postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: chatwoot_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: chatwoot123
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存服务
  redis:
    image: docker.io/library/redis:7-alpine
    container_name: chatwoot_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass chatwoot123 --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "chatwoot123", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MailHog邮件测试服务
  mailhog:
    image: docker.io/mailhog/mailhog:latest
    container_name: chatwoot_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI

  # Rails应用服务
  rails:
    build:
      context: .
      dockerfile: Containerfile.dev
      target: development
    image: chatwoot:development
    container_name: chatwoot_rails
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mailhog:
        condition: service_started
    environment:
      # 数据库配置
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DATABASE: chatwoot_dev
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: chatwoot123
      
      # Redis配置
      REDIS_URL: redis://:chatwoot123@redis:6379/0
      
      # Rails配置
      RAILS_ENV: development
      NODE_ENV: development
      SECRET_KEY_BASE: development_secret_key_base_at_least_30_chars_long_for_security
      RAILS_LOG_TO_STDOUT: "true"
      RAILS_SERVE_STATIC_FILES: "true"
      
      # 邮件配置
      SMTP_ADDRESS: mailhog
      SMTP_PORT: 1025
      SMTP_DOMAIN: mailhog
      MAILER_SMTP_DOMAIN: mailhog
      
      # 开发模式配置
      WEBPACKER_DEV_SERVER_HOST: vite
      WEBPACKER_DEV_SERVER_PORT: 3036
    volumes:
      - .:/app:delegated
      - gem_cache:/usr/local/bundle
      - node_modules:/app/node_modules
      - tmp_cache:/app/tmp
    command: >
      sh -c "
        rm -f /app/tmp/pids/server.pid &&
        bundle exec rails db:create db:migrate &&
        bundle exec rails server -b 0.0.0.0 -p 3000
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Sidekiq后台任务处理
  sidekiq:
    image: chatwoot:development
    container_name: chatwoot_sidekiq
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rails:
        condition: service_started
    environment:
      # 数据库配置
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DATABASE: chatwoot_dev
      POSTGRES_USERNAME: postgres
      POSTGRES_PASSWORD: chatwoot123
      
      # Redis配置
      REDIS_URL: redis://:chatwoot123@redis:6379/0
      
      # 环境配置
      RAILS_ENV: development
      NODE_ENV: development
      SECRET_KEY_BASE: development_secret_key_base_at_least_30_chars_long_for_security
      SIDEKIQ_CONCURRENCY: 5
    volumes:
      - .:/app:delegated
      - gem_cache:/usr/local/bundle
      - node_modules:/app/node_modules
      - tmp_cache:/app/tmp
    command: ["bundle", "exec", "sidekiq", "-C", "config/sidekiq.yml"]

  # Vite前端开发服务器
  vite:
    image: chatwoot:development
    container_name: chatwoot_vite
    restart: unless-stopped
    ports:
      - "3036:3036"
    environment:
      NODE_ENV: development
      VITE_DEV_SERVER_HOST: 0.0.0.0
      VITE_DEV_SERVER_PORT: 3036
      VITE_HMR_PORT: 3036
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
    volumes:
      - .:/app:delegated
      - node_modules:/app/node_modules
    command: >
      sh -c "
        cd /app &&
        pnpm install --force &&
        bin/vite dev
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3036/vite-dev/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    name: chatwoot_postgres_data
  redis_data:
    name: chatwoot_redis_data
  gem_cache:
    name: chatwoot_gem_cache
  node_modules:
    name: chatwoot_node_modules
  tmp_cache:
    name: chatwoot_tmp_cache
