#!/bin/bash
# podman-dev.sh - Chatwoot Podman开发环境管理脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
COMPOSE_FILE="docker-compose.podman.yaml"
PROJECT_NAME="chatwoot-dev"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查先决条件
check_prerequisites() {
    log_step "检查系统先决条件..."
    
    # 检查Podman
    if ! command -v podman &> /dev/null; then
        log_error "Podman未安装，请先安装Podman"
        echo "macOS: brew install podman"
        echo "Linux: sudo apt install podman"
        exit 1
    fi
    
    # 检查podman-compose
    if ! command -v podman-compose &> /dev/null; then
        log_warn "podman-compose未安装，将使用podman play kube"
        log_info "建议安装: pip3 install podman-compose"
    fi
    
    # 检查项目文件
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "找不到配置文件: $COMPOSE_FILE"
        exit 1
    fi
    
    if [[ ! -f "Containerfile.dev" ]]; then
        log_error "找不到Containerfile.dev"
        exit 1
    fi
    
    log_info "系统先决条件检查通过"
}

# 创建环境文件
create_env_file() {
    if [[ ! -f ".env" ]]; then
        log_step "创建环境配置文件..."
        cat > .env << EOF
# 数据库配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DATABASE=chatwoot_dev
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=chatwoot123

# Redis配置
REDIS_URL=redis://:chatwoot123@redis:6379/0
REDIS_PASSWORD=chatwoot123

# Rails配置
RAILS_ENV=development
NODE_ENV=development
SECRET_KEY_BASE=development_secret_key_base_at_least_30_chars_long_for_security
RAILS_LOG_TO_STDOUT=true
RAILS_SERVE_STATIC_FILES=true

# 邮件配置
SMTP_ADDRESS=mailhog
SMTP_PORT=1025
SMTP_DOMAIN=mailhog
MAILER_SMTP_DOMAIN=mailhog

# 开发模式配置
WEBPACKER_DEV_SERVER_HOST=vite
WEBPACKER_DEV_SERVER_PORT=3036
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=3036
EOF
        log_info "环境配置文件已创建"
    else
        log_info "环境配置文件已存在"
    fi
}

# 构建镜像
build_images() {
    log_step "构建Chatwoot开发镜像..."
    
    podman build -t chatwoot:development \
        --target development \
        --file Containerfile.dev \
        .
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_step "启动Chatwoot开发环境..."
    
    if command -v podman-compose &> /dev/null; then
        # 使用podman-compose
        podman-compose -f "$COMPOSE_FILE" up -d
    else
        # 使用podman run命令逐个启动服务
        start_services_manual
    fi
    
    log_info "等待服务启动..."
    sleep 30
    
    # 初始化数据库
    log_step "初始化数据库..."
    podman exec chatwoot_rails bundle exec rails db:create db:migrate db:seed || true
    
    log_info "服务启动完成"
}

# 手动启动服务（当没有podman-compose时）
start_services_manual() {
    log_info "使用podman run手动启动服务..."
    
    # 创建网络
    podman network create chatwoot-network || true
    
    # 启动PostgreSQL
    podman run -d \
        --name chatwoot_postgres \
        --network chatwoot-network \
        -p 5432:5432 \
        -e POSTGRES_DB=chatwoot_dev \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=chatwoot123 \
        -v chatwoot_postgres_data:/var/lib/postgresql/data \
        docker.io/pgvector/pgvector:pg16
    
    # 启动Redis
    podman run -d \
        --name chatwoot_redis \
        --network chatwoot-network \
        -p 6379:6379 \
        -v chatwoot_redis_data:/data \
        docker.io/library/redis:7-alpine \
        redis-server --requirepass chatwoot123 --appendonly yes
    
    # 启动MailHog
    podman run -d \
        --name chatwoot_mailhog \
        --network chatwoot-network \
        -p 1025:1025 \
        -p 8025:8025 \
        docker.io/mailhog/mailhog:latest
    
    # 等待数据库启动
    sleep 10
    
    # 启动Rails
    podman run -d \
        --name chatwoot_rails \
        --network chatwoot-network \
        -p 3000:3000 \
        -v "$(pwd):/app:delegated" \
        -v chatwoot_gem_cache:/usr/local/bundle \
        -v chatwoot_node_modules:/app/node_modules \
        --env-file .env \
        chatwoot:development \
        sh -c "rm -f /app/tmp/pids/server.pid && bundle exec rails server -b 0.0.0.0 -p 3000"
    
    # 启动Sidekiq
    podman run -d \
        --name chatwoot_sidekiq \
        --network chatwoot-network \
        -v "$(pwd):/app:delegated" \
        -v chatwoot_gem_cache:/usr/local/bundle \
        --env-file .env \
        chatwoot:development \
        bundle exec sidekiq -C config/sidekiq.yml
    
    # 启动Vite
    podman run -d \
        --name chatwoot_vite \
        --network chatwoot-network \
        -p 3036:3036 \
        -v "$(pwd):/app:delegated" \
        -v chatwoot_node_modules:/app/node_modules \
        -e NODE_ENV=development \
        -e VITE_DEV_SERVER_HOST=0.0.0.0 \
        -e VITE_DEV_SERVER_PORT=3036 \
        chatwoot:development \
        sh -c "cd /app && pnpm install --force && bin/vite dev"
}

# 停止服务
stop_services() {
    log_step "停止Chatwoot开发环境..."
    
    if command -v podman-compose &> /dev/null; then
        podman-compose -f "$COMPOSE_FILE" down
    else
        # 手动停止容器
        containers=("chatwoot_vite" "chatwoot_sidekiq" "chatwoot_rails" "chatwoot_mailhog" "chatwoot_redis" "chatwoot_postgres")
        for container in "${containers[@]}"; do
            podman stop "$container" 2>/dev/null || true
            podman rm "$container" 2>/dev/null || true
        done
        podman network rm chatwoot-network 2>/dev/null || true
    fi
    
    log_info "服务已停止"
}

# 查看状态
show_status() {
    log_step "查看服务状态..."
    
    echo ""
    log_info "容器状态:"
    podman ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    log_info "卷状态:"
    podman volume ls | grep chatwoot || echo "未找到相关卷"
    
    echo ""
    log_info "服务访问地址:"
    echo "🌐 Chatwoot Web应用: http://localhost:3036"
    echo "🔧 Rails API: http://localhost:3000"
    echo "📧 MailHog: http://localhost:8025"
    echo "💾 PostgreSQL: localhost:5432"
    echo "🔄 Redis: localhost:6379"
}

# 查看日志
show_logs() {
    local service=${1:-rails}
    log_step "查看 $service 服务日志..."
    podman logs -f "chatwoot_$service"
}

# 进入容器
exec_container() {
    local service=${1:-rails}
    local cmd=${2:-/bin/sh}
    log_step "进入 $service 容器..."
    podman exec -it "chatwoot_$service" "$cmd"
}

# 重置环境
reset_environment() {
    log_warn "⚠️  这将删除所有数据和容器！"
    read -p "确认重置环境？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_step "重置开发环境..."
        stop_services
        
        # 删除卷
        volumes=("chatwoot_postgres_data" "chatwoot_redis_data" "chatwoot_gem_cache" "chatwoot_node_modules" "chatwoot_tmp_cache")
        for volume in "${volumes[@]}"; do
            podman volume rm "$volume" 2>/dev/null || true
        done
        
        # 删除镜像
        podman rmi chatwoot:development 2>/dev/null || true
        
        log_info "环境重置完成"
    else
        log_info "取消重置操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Chatwoot Podman开发环境管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动开发环境"
    echo "  stop      停止开发环境"
    echo "  restart   重启开发环境"
    echo "  status    查看服务状态"
    echo "  logs      查看日志 [服务名]"
    echo "  exec      进入容器 [服务名] [命令]"
    echo "  build     构建镜像"
    echo "  reset     重置环境（删除所有数据）"
    echo "  help      显示帮助信息"
    echo ""
    echo "服务名: rails, sidekiq, vite, postgres, redis, mailhog"
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动开发环境"
    echo "  $0 logs rails              # 查看Rails日志"
    echo "  $0 exec rails bundle exec rails console  # 进入Rails控制台"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_prerequisites
            create_env_file
            build_images
            start_services
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 5
            start_services
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "exec")
            exec_container "$2" "$3"
            ;;
        "build")
            check_prerequisites
            build_images
            ;;
        "reset")
            reset_environment
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
