#!/bin/bash
# verify-podman-setup.sh - 验证Podman开发环境设置

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_fail() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查计数器
checks_passed=0
checks_total=0

# 检查函数
check_command() {
    local cmd=$1
    local name=$2
    checks_total=$((checks_total + 1))
    
    if command -v "$cmd" &> /dev/null; then
        log_success "$name 已安装"
        checks_passed=$((checks_passed + 1))
        return 0
    else
        log_fail "$name 未安装"
        return 1
    fi
}

check_file() {
    local file=$1
    local name=$2
    checks_total=$((checks_total + 1))
    
    if [[ -f "$file" ]]; then
        log_success "$name 存在"
        checks_passed=$((checks_passed + 1))
        return 0
    else
        log_fail "$name 不存在"
        return 1
    fi
}

check_service() {
    local url=$1
    local name=$2
    local timeout=${3:-5}
    checks_total=$((checks_total + 1))
    
    if curl -f -s --max-time "$timeout" "$url" >/dev/null 2>&1; then
        log_success "$name 服务正常"
        checks_passed=$((checks_passed + 1))
        return 0
    else
        log_fail "$name 服务不可用"
        return 1
    fi
}

# 主验证函数
main() {
    log_step "开始验证Podman开发环境设置..."
    echo ""
    
    # 1. 检查系统工具
    log_step "1. 检查系统工具"
    check_command "podman" "Podman"
    check_command "curl" "cURL"
    check_command "git" "Git"
    
    # 检查podman-compose（可选）
    if command -v podman-compose &> /dev/null; then
        log_success "podman-compose 已安装（推荐）"
    else
        log_warn "podman-compose 未安装（可选，建议安装以获得更好体验）"
        echo "  安装命令: pip3 install podman-compose"
    fi
    echo ""
    
    # 2. 检查Podman状态
    log_step "2. 检查Podman状态"
    checks_total=$((checks_total + 1))
    if podman info >/dev/null 2>&1; then
        log_success "Podman 运行正常"
        checks_passed=$((checks_passed + 1))
        
        # 检查无根模式
        if podman info | grep -q "rootless"; then
            log_success "Podman 无根模式已启用"
        else
            log_warn "Podman 未运行在无根模式"
        fi
        
        # 显示版本信息
        local version=$(podman --version)
        log_info "版本: $version"
    else
        log_fail "Podman 无法正常运行"
        echo "  请检查Podman是否正确安装和配置"
    fi
    echo ""
    
    # 3. 检查项目文件
    log_step "3. 检查项目配置文件"
    check_file "Containerfile.dev" "开发环境Containerfile"
    check_file "docker-compose.podman.yaml" "Podman Compose配置"
    check_file ".env.podman" "Podman环境配置"
    check_file "scripts/podman-dev.sh" "Podman管理脚本"
    
    # 检查脚本权限
    checks_total=$((checks_total + 1))
    if [[ -x "scripts/podman-dev.sh" ]]; then
        log_success "管理脚本具有执行权限"
        checks_passed=$((checks_passed + 1))
    else
        log_fail "管理脚本缺少执行权限"
        echo "  修复命令: chmod +x scripts/podman-dev.sh"
    fi
    echo ""
    
    # 4. 检查Ruby和Node.js环境（如果本地安装）
    log_step "4. 检查开发工具（可选）"
    if command -v ruby &> /dev/null; then
        local ruby_version=$(ruby --version)
        log_info "Ruby: $ruby_version"
    else
        log_info "Ruby 未在本地安装（将在容器中使用）"
    fi
    
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        log_info "Node.js: $node_version"
    else
        log_info "Node.js 未在本地安装（将在容器中使用）"
    fi
    
    if command -v pnpm &> /dev/null; then
        local pnpm_version=$(pnpm --version)
        log_info "PNPM: v$pnpm_version"
    else
        log_info "PNPM 未在本地安装（将在容器中使用）"
    fi
    echo ""
    
    # 5. 检查端口占用
    log_step "5. 检查端口占用情况"
    ports=(3000 3036 5432 6379 8025)
    port_conflicts=0
    
    for port in "${ports[@]}"; do
        if lsof -i ":$port" >/dev/null 2>&1; then
            log_warn "端口 $port 已被占用"
            port_conflicts=$((port_conflicts + 1))
            # 显示占用进程
            local process=$(lsof -i ":$port" | tail -n 1 | awk '{print $1 " (PID: " $2 ")"}')
            echo "  占用进程: $process"
        else
            log_success "端口 $port 可用"
        fi
    done
    
    if [[ $port_conflicts -gt 0 ]]; then
        echo ""
        log_warn "发现 $port_conflicts 个端口冲突，可能需要停止相关服务"
        echo "  停止命令示例: sudo kill -9 <PID>"
    fi
    echo ""
    
    # 6. 检查磁盘空间
    log_step "6. 检查磁盘空间"
    local available_space=$(df -h . | tail -1 | awk '{print $4}')
    log_info "当前目录可用空间: $available_space"
    
    # 简单检查是否有足够空间（至少5GB）
    local space_gb=$(df . | tail -1 | awk '{print int($4/1024/1024)}')
    if [[ $space_gb -gt 5 ]]; then
        log_success "磁盘空间充足"
    else
        log_warn "磁盘空间可能不足，建议至少保留5GB空间"
    fi
    echo ""
    
    # 7. 如果服务正在运行，检查服务状态
    log_step "7. 检查运行中的服务（如果有）"
    if podman ps | grep -q chatwoot; then
        log_info "发现运行中的Chatwoot服务"
        
        # 检查各个服务
        check_service "http://localhost:3000/health" "Rails API" 10
        check_service "http://localhost:3036" "Vite开发服务器" 10
        check_service "http://localhost:8025" "MailHog邮件服务" 5
        
        # 检查数据库连接
        if podman exec chatwoot_postgres pg_isready -U postgres >/dev/null 2>&1; then
            log_success "PostgreSQL 数据库连接正常"
        else
            log_fail "PostgreSQL 数据库连接失败"
        fi
        
        # 检查Redis连接
        if podman exec chatwoot_redis redis-cli -a chatwoot123 ping >/dev/null 2>&1; then
            log_success "Redis 缓存连接正常"
        else
            log_fail "Redis 缓存连接失败"
        fi
    else
        log_info "未发现运行中的Chatwoot服务"
        echo "  启动命令: ./scripts/podman-dev.sh start"
    fi
    echo ""
    
    # 8. 总结
    log_step "验证总结"
    echo "检查项目: $checks_total"
    echo "通过项目: $checks_passed"
    echo "失败项目: $((checks_total - checks_passed))"
    echo ""
    
    if [[ $checks_passed -eq $checks_total ]]; then
        log_success "🎉 所有检查都通过了！环境配置正确。"
        echo ""
        echo "下一步操作:"
        echo "  1. 启动开发环境: ./scripts/podman-dev.sh start"
        echo "  2. 访问应用: http://localhost:3036"
        echo "  3. 查看邮件: http://localhost:8025"
        return 0
    elif [[ $checks_passed -gt $((checks_total * 3 / 4)) ]]; then
        log_warn "⚠️  大部分检查通过，但有一些问题需要注意。"
        echo ""
        echo "建议:"
        echo "  1. 解决上述警告和错误"
        echo "  2. 然后尝试启动: ./scripts/podman-dev.sh start"
        return 1
    else
        log_error "❌ 发现多个问题，请先解决后再启动环境。"
        echo ""
        echo "常见解决方案:"
        echo "  1. 安装缺失的工具"
        echo "  2. 检查Podman配置"
        echo "  3. 解决端口冲突"
        echo "  4. 确保有足够的磁盘空间"
        return 2
    fi
}

# 显示帮助信息
show_help() {
    echo "Podman开发环境验证脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo "  -q, --quiet   静默模式（仅显示错误）"
    echo "  -v, --verbose 详细模式（显示更多信息）"
    echo ""
    echo "此脚本将检查:"
    echo "  - Podman安装和配置"
    echo "  - 项目配置文件"
    echo "  - 端口占用情况"
    echo "  - 磁盘空间"
    echo "  - 运行中的服务状态"
}

# 处理命令行参数
case "${1:-}" in
    "-h"|"--help")
        show_help
        exit 0
        ;;
    "-q"|"--quiet")
        # 静默模式实现（简化版）
        exec 1>/dev/null
        ;;
    "-v"|"--verbose")
        # 详细模式（默认已经很详细）
        set -x
        ;;
esac

# 执行主函数
main "$@"
